package com.youying.web.controller;

import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.youying.common.annotation.Log;
import com.youying.common.core.common.PullResponse;
import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.Repertoire;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.common.enums.Enums.AuditFlag;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.common.utils.WechatUtil;
import com.youying.common.utils.file.FileUploadUtils;
import com.youying.system.domain.common.AuditRequest;
import com.youying.system.domain.common.PullRequest;
import com.youying.system.domain.repertoire.AddRepertoireLabelRequest;
import com.youying.system.domain.repertoire.RepertoireRequest;
import com.youying.system.domain.repertoire.RepertoireResponse;
import com.youying.system.service.RepertoireLabelService;
import com.youying.system.service.RepertoireService;

import lombok.extern.slf4j.Slf4j;

/**
 * 剧目表
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@RestController
@RequestMapping("/repertoire")
@Slf4j
public class RepertoireController extends BaseController {
    @Autowired
    private RepertoireService repertoireService;
    @Autowired
    private RepertoireLabelService repertoireLabelService;

    /**
     * 查询剧目表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<RepertoireResponse>> listByPage(@RequestBody RepertoireRequest request) {
        startPage(request);
        return R.ok(getTableList(repertoireService.listByPage(request)));
    }

    /**
     * 查询剧目表详情(部分)
     *
     * @return
     */
    @GetMapping(value = "/findRepertoireInfo")
    public R<RepertoireResponse> findRepertoireInfo(Long id) {
        return R.ok(repertoireService.findRepertoireInfo(id));
    }

    /**
     * 查询剧目表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<Repertoire> details(Long id) {
        return R.ok(repertoireService.getById(id));
    }

    /**
     * 修改剧目标签
     *
     * @return
     */
    @PutMapping(value = "/updateLabel")
    @Log(title = "修改剧目标签", businessType = BusinessType.UPDATE)
    public R<?> updateLabel(@RequestBody AddRepertoireLabelRequest request) {
        repertoireLabelService.updateLabel(request);
        return R.ok();
    }

    /**
     * 修改剧目推荐状态
     *
     * @return
     */
    @PutMapping(value = "/updateRecommend/{id}")
    @Log(title = "修改剧目推荐状态", businessType = BusinessType.UPDATE)
    public R<?> updateRecommend(@PathVariable("id") Long id) {
        return R.ok(repertoireService.updateRecommend(id));
    }

    /**
     * 修改剧目启用状态
     *
     * @return
     */
    @PutMapping(value = "/updateStatus/{id}")
    @Log(title = "修改剧目启用状态", businessType = BusinessType.UPDATE)
    public R<?> updateStatus(@PathVariable("id") Long id) {
        Repertoire repertoire = repertoireService.getById(id);
        Integer status = StatusFlag.OK.getCode().equals(repertoire.getStatus()) ? StatusFlag.PROHIBITION.getCode()
                : StatusFlag.OK.getCode();
        repertoire.setStatus(status);
        repertoireService.updateById(repertoire);
        return R.ok();
    }

    /**
     * 剧目审核
     *
     * @return
     */
    @PutMapping(value = "/audit")
    @Log(title = "剧目审核", businessType = BusinessType.UPDATE)
    public R<?> audit(@RequestBody AuditRequest request) {
        Repertoire repertoire = repertoireService.getById(request.getId());
        if (AuditFlag.PASS.getCode().equals(repertoire.getAudit())) {
            return R.fail("请勿重复审核");
        }
        if (AuditFlag.PASS.getCode().equals(request.getAudit())) {
            repertoire.setAuditPassTime(new Date());
            repertoire.setReasonsRejection(null);
            repertoire.setShortName(request.getShortName());
            repertoire.setStatus(StatusFlag.OK.getCode());
            if (AuditFlag.PASS.getCode().equals(request.getAudit())
                    && AuditFlag.WAIT.getCode().equals(repertoire.getAuditFlag())) {
                repertoire.setAuditFlag(request.getAudit());
                // 生成二维码
                try {
                    String uploadUrl = FileUploadUtils
                            .upload(WechatUtil.sendWechatQr("pages/sub/detail/repertoire", repertoire.getId()));
                    repertoire.setQrCode(uploadUrl);
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        repertoire.setStatus(StatusFlag.PROHIBITION.getCode());
        repertoire.setAudit(request.getAudit());
        repertoire.setReasonsRejection(request.getReasonsRejection());
        repertoireService.updateById(repertoire);
        return R.ok();
    }

    /**
     * 删除剧目表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除剧目表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(repertoireService.delete(Arrays.asList(ids)));
        }
        return R.ok();
    }

    /**
     * 剧目下拉
     *
     * @return
     */
    @PostMapping(value = "/pull")
    public R<List<PullResponse>> pull(@RequestBody PullRequest request) {
        return R.ok(repertoireService.pull(request));
    }

    /**
     * 首页-查询剧目段添加数量
     *
     * @return
     */
    @PostMapping(value = "/findRepertoireAddCount")
    public R<?> findRepertoireAddCount(@RequestBody TimeRequest time) {
        return R.ok(repertoireService.findRepertoireAddCount(time));
    }

    /**
     * 修改剧目信息
     *
     * @param repertoire 剧目信息
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改剧目信息", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody Repertoire repertoire) {
        if (repertoire.getId() == null) {
            return R.fail("剧目ID不能为空");
        }

        // 检查剧目是否存在
        Repertoire existingRepertoire = repertoireService.getById(repertoire.getId());
        if (existingRepertoire == null) {
            return R.fail("剧目不存在");
        }

        // 更新修改时间
        repertoire.setUpdateTime(new Date());

        boolean result = repertoireService.updateById(repertoire);
        if (result) {
            return R.ok("修改成功");
        } else {
            return R.fail("修改失败");
        }
    }

}
