package com.youying.web.controller.system;

import com.youying.common.cache.CacheRefreshManager;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.AjaxResult;
import com.youying.common.event.DatabaseChangeEvent;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 缓存刷新控制器
 * 提供手动触发缓存刷新的接口
 *
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "缓存刷新管理")
@RestController
@RequestMapping("/system/cache/refresh")
public class CacheRefreshController extends BaseController {

    @Autowired
    private CacheRefreshManager cacheRefreshManager;

    /**
     * 获取支持的表名列表
     */
    @ApiOperation("获取支持的表名列表")
    @PreAuthorize("@ss.hasPermi('system:cache:list')")
    @GetMapping("/tables")
    public AjaxResult getSupportedTables() {
        try {
            List<String> tables = cacheRefreshManager.getSupportedTables();
            return AjaxResult.success("获取成功", tables);
        } catch (Exception e) {
            log.error("获取支持的表名列表失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 手动刷新指定表的缓存
     */
    @ApiOperation("手动刷新指定表的缓存")
    @PreAuthorize("@ss.hasPermi('system:cache:refresh')")
    @PostMapping("/manual")
    public AjaxResult manualRefresh(
            @ApiParam(value = "表名", required = true) @RequestParam String tableName,
            @ApiParam(value = "操作类型", required = true) @RequestParam String operationType,
            @ApiParam(value = "主键ID") @RequestParam(required = false) Long primaryKey) {
        
        try {
            // 验证操作类型
            DatabaseChangeEvent.OperationType opType;
            try {
                opType = DatabaseChangeEvent.OperationType.valueOf(operationType.toUpperCase());
            } catch (IllegalArgumentException e) {
                return AjaxResult.error("无效的操作类型: " + operationType + 
                    "，支持的类型: INSERT, UPDATE, DELETE");
            }

            // 触发缓存刷新
            cacheRefreshManager.manualRefresh(tableName, opType, primaryKey);
            
            String message = String.format("成功触发表 %s 的缓存刷新，操作类型: %s，主键: %s", 
                tableName, operationType, primaryKey);
            log.info(message);
            
            return AjaxResult.success(message);
            
        } catch (Exception e) {
            String errorMsg = String.format("手动刷新缓存失败，表: %s，操作: %s，主键: %s，错误: %s", 
                tableName, operationType, primaryKey, e.getMessage());
            log.error(errorMsg, e);
            return AjaxResult.error(errorMsg);
        }
    }

    /**
     * 刷新剧目相关缓存
     */
    @ApiOperation("刷新剧目相关缓存")
    @PreAuthorize("@ss.hasPermi('system:cache:refresh')")
    @PostMapping("/repertoire/{id}")
    public AjaxResult refreshRepertoireCache(
            @ApiParam(value = "剧目ID", required = true) @PathVariable Long id,
            @ApiParam(value = "操作类型", required = false, defaultValue = "UPDATE") 
            @RequestParam(defaultValue = "UPDATE") String operationType) {
        
        return manualRefresh("t_repertoire", operationType, id);
    }

    /**
     * 刷新剧场相关缓存
     */
    @ApiOperation("刷新剧场相关缓存")
    @PreAuthorize("@ss.hasPermi('system:cache:refresh')")
    @PostMapping("/theater/{id}")
    public AjaxResult refreshTheaterCache(
            @ApiParam(value = "剧场ID", required = true) @PathVariable Long id,
            @ApiParam(value = "操作类型", required = false, defaultValue = "UPDATE") 
            @RequestParam(defaultValue = "UPDATE") String operationType) {
        
        return manualRefresh("t_theater", operationType, id);
    }

    /**
     * 批量刷新缓存
     */
    @ApiOperation("批量刷新缓存")
    @PreAuthorize("@ss.hasPermi('system:cache:refresh')")
    @PostMapping("/batch")
    public AjaxResult batchRefresh(@RequestBody BatchRefreshRequest request) {
        try {
            int successCount = 0;
            int failCount = 0;
            StringBuilder errorMessages = new StringBuilder();

            for (RefreshItem item : request.getItems()) {
                try {
                    DatabaseChangeEvent.OperationType opType = 
                        DatabaseChangeEvent.OperationType.valueOf(item.getOperationType().toUpperCase());
                    cacheRefreshManager.manualRefresh(item.getTableName(), opType, item.getPrimaryKey());
                    successCount++;
                } catch (Exception e) {
                    failCount++;
                    errorMessages.append(String.format("表 %s (ID: %s): %s; ", 
                        item.getTableName(), item.getPrimaryKey(), e.getMessage()));
                }
            }

            String message = String.format("批量刷新完成，成功: %d，失败: %d", successCount, failCount);
            if (failCount > 0) {
                message += "，失败详情: " + errorMessages.toString();
            }

            log.info(message);
            return failCount == 0 ? AjaxResult.success(message) : AjaxResult.warn(message);

        } catch (Exception e) {
            log.error("批量刷新缓存失败: {}", e.getMessage(), e);
            return AjaxResult.error("批量刷新失败: " + e.getMessage());
        }
    }

    /**
     * 批量刷新请求对象
     */
    public static class BatchRefreshRequest {
        private List<RefreshItem> items;

        public List<RefreshItem> getItems() {
            return items;
        }

        public void setItems(List<RefreshItem> items) {
            this.items = items;
        }
    }

    /**
     * 刷新项对象
     */
    public static class RefreshItem {
        private String tableName;
        private String operationType;
        private Long primaryKey;

        public String getTableName() {
            return tableName;
        }

        public void setTableName(String tableName) {
            this.tableName = tableName;
        }

        public String getOperationType() {
            return operationType;
        }

        public void setOperationType(String operationType) {
            this.operationType = operationType;
        }

        public Long getPrimaryKey() {
            return primaryKey;
        }

        public void setPrimaryKey(Long primaryKey) {
            this.primaryKey = primaryKey;
        }
    }
}
