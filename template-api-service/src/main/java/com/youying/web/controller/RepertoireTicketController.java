package com.youying.web.controller;

import java.util.Arrays;
import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.RepertoireTicket;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.common.enums.Enums.AuditFlag;
import com.youying.common.utils.StringUtils;
import com.youying.system.domain.common.AuditRequest;
import com.youying.system.domain.repertoireticket.RepertoireTicketRequest;
import com.youying.system.domain.repertoireticket.RepertoireTicketResponse;
import com.youying.system.service.RepertoireTicketService;

import lombok.extern.slf4j.Slf4j;

/**
 * 剧目电子票表
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@Slf4j
@RestController
@RequestMapping("/repertoireTicket")
public class RepertoireTicketController extends BaseController {
    @Autowired
    private RepertoireTicketService repertoireTicketService;

    /**
     * 查询剧目电子票表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<RepertoireTicketResponse>> listByPage(@RequestBody RepertoireTicketRequest request) {
        startPage(request);
        return R.ok(getTableList(repertoireTicketService.listByPage(request)));
    }

    /**
     * 电子票审核
     *
     * @return
     */
    @PutMapping(value = "/audit")
    @Log(title = "电子票审核", businessType = BusinessType.UPDATE)
    public R<?> audit(@RequestBody AuditRequest request) {
        RepertoireTicket repertoireTicket = repertoireTicketService.getById(request.getId());
        if (AuditFlag.PASS.getCode().equals(repertoireTicket.getAudit())) {
            return R.fail("请勿重复审核");
        }

        if (AuditFlag.PASS.getCode().equals(request.getAudit())) {
            repertoireTicket.setAuditPassTime(new Date());
            repertoireTicket.setReasonsRejection(null);
            repertoireTicket.setAudit(AuditFlag.PASS.getCode());
            if (AuditFlag.PASS.getCode().equals(request.getAudit())
                    && AuditFlag.WAIT.getCode().equals(repertoireTicket.getAuditFlag())) {
                repertoireTicket.setAuditFlag(request.getAudit());
            }
            repertoireTicketService.updateById(repertoireTicket);
            return R.ok();
        } else {
            if (StringUtils.isBlank(request.getReasonsRejection())) {
                return R.fail("驳回原因不能为空");
            }
            // 驳回
            repertoireTicket.setAudit(request.getAudit());
            repertoireTicket.setReasonsRejection(request.getReasonsRejection());
            repertoireTicketService.updateById(repertoireTicket);
        }

        return R.ok();
    }

    /**
     * 删除电子票表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除剧目电子票表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(repertoireTicketService.delete(Arrays.asList(ids)));
        }
        return R.ok();
    }

    /**
     * 修改电子票封面
     *
     * @param ticket
     * @return
     */
    @PutMapping(value = "/updateCommonImage")
    public R<?> updateCommonImage(@RequestBody RepertoireTicket ticket) {
        repertoireTicketService.updateCommonImage(ticket);
        return R.ok();
    }

}
