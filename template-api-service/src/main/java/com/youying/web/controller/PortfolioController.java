package com.youying.web.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.Portfolio;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.common.enums.Enums.AuditFlag;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.system.domain.portfolio.PortfolioRequest;
import com.youying.system.domain.portfolio.PortfolioResponse;
import com.youying.system.domain.portfolio.PortfolioWithInfoRequest;
import com.youying.system.domain.portfolio.PortfolioWithInfoResponse;
import com.youying.system.service.PortfolioService;

/**
 * 藏品组合表
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
@RestController
@RequestMapping("/portfolio")
public class PortfolioController extends BaseController {

    @Autowired
    private PortfolioService portfolioService;

    /**
     * 添加藏品组合表
     *
     * @param portfolio 藏品组合信息
     * @return 新增结果
     */
    @PostMapping(value = "/add")
    @Log(title = "添加藏品组合表", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody Portfolio portfolio) {
        return R.ok(portfolioService.save(portfolio));
    }

    /**
     * 查询藏品组合表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<PortfolioResponse>> listByPage(@RequestBody PortfolioRequest request) {
        startPage(request);
        TableList<PortfolioResponse> list = getTableList(portfolioService.listByPage(request));
        return R.ok(list);
    }

    /**
     * 查询藏品组合表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<PortfolioResponse> details(Long id) {
        return R.ok(portfolioService.details(id));
    }

    /**
     * 修改藏品组合
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改藏品组合", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody Portfolio portfolio) {
        return R.ok(portfolioService.update(portfolio));
    }

    /**
     * 删除藏品组合表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除藏品组合表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(portfolioService.delete(ids));
        }
        return R.ok();
    }

    /**
     * 商品信息审核
     *
     * @return
     */
    @PutMapping(value = "/audit")
    @Log(title = "商品信息审核", businessType = BusinessType.UPDATE)
    public R<?> audit(@RequestBody Portfolio portfolio) {
        Portfolio portfolioInfo = portfolioService.getById(portfolio.getId());
        if (AuditFlag.PASS.getCode().equals(portfolioInfo.getAudit())) {
            return R.fail("请勿重复审核");
        }
        return R.ok(portfolioService.audit(portfolio));
    }

    /**
     * 修改商品电子票、数字头显展示
     *
     * @return
     */
    @PutMapping(value = "/updateLookStatus/{id}")
    @Log(title = "修改商品电子票、数字头显展示", businessType = BusinessType.UPDATE)
    public R<?> updateLookStatus(@PathVariable("id") Long id) {
        Portfolio portfolioInfo = portfolioService.getById(id);
        Integer lookStatus = StatusFlag.toggleStatus(portfolioInfo.getLookStatus());
        portfolioInfo.setLookStatus(lookStatus);
        return R.ok(portfolioService.updateById(portfolioInfo));
    }

    /**
     * 修改商品座位是否唯一
     *
     * @return
     */
    @PutMapping(value = "/updateSeatStatus/{id}")
    @Log(title = "修改商品座位是否唯一", businessType = BusinessType.UPDATE)
    public R<?> updateSeatStatus(@PathVariable("id") Long id) {
        Portfolio portfolioInfo = portfolioService.getById(id);
        Integer seatStatus = StatusFlag.toggleStatus(portfolioInfo.getSeatStatus());
        portfolioInfo.setSeatStatus(seatStatus);
        return R.ok(portfolioService.updateById(portfolioInfo));
    }

    /**
     * 修改电子票面中间是否需要组合剧目名称
     *
     * @return
     */
    @PutMapping(value = "/updateIfCompositeRepertoireName/{id}")
    @Log(title = "修改商品是否组合剧目名称", businessType = BusinessType.UPDATE)
    public R<?> updateIfCompositeRepertoireName(@PathVariable("id") Long id) {
        Portfolio portfolioInfo = portfolioService.getById(id);
        Boolean ifCompositeRepertoireName = !portfolioInfo.getIfCompositeRepertoireName();
        portfolioInfo.setIfCompositeRepertoireName(ifCompositeRepertoireName);
        return R.ok(portfolioService.updateById(portfolioInfo));
    }

    // ========== 以下为Portfolio与PortfolioInfo关联的增删查改方法 ==========

    /**
     * 新增Portfolio与PortfolioInfo关联记录
     *
     * @param request 请求参数
     * @return 新增结果，返回新增记录的Portfolio ID
     */
    @PostMapping(value = "/portfolioInfoCreate")
    @Log(title = "新增Portfolio与PortfolioInfo关联记录", businessType = BusinessType.INSERT)
    public R<Long> portfolioInfoCreate(@RequestBody PortfolioWithInfoRequest request) {

        try {
            Long portfolioId = portfolioService.portfolioInfoCreate(request);
            return R.ok(portfolioId, "新增Portfolio与PortfolioInfo关联记录成功");
        } catch (Exception e) {
            return R.fail("新增Portfolio与PortfolioInfo关联记录失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID查询Portfolio与PortfolioInfo关联详情
     *
     * @param id Portfolio ID
     * @return 详情信息
     */
    @GetMapping(value = "/portfolioInfoDetails")
    public R<PortfolioWithInfoResponse> portfolioInfoDetails(Long id) {
        if (id == null) {
            return R.fail("Portfolio ID不能为空");
        }

        PortfolioWithInfoResponse response = portfolioService.portfolioInfoDetails(id);
        if (response == null) {
            return R.fail("记录不存在");
        }

        return R.ok(response);
    }

    /**
     * 分页查询Portfolio与PortfolioInfo关联列表
     *
     * @param request 查询条件
     * @return 列表数据
     */
    @PostMapping(value = "/portfolioInfoListByPage")
    public R<TableList<PortfolioWithInfoResponse>> portfolioInfoListByPage(@RequestBody PortfolioRequest request) {
        startPage(request);
        return R.ok(getTableList(portfolioService.portfolioInfoListByPage(request)));
    }

    /**
     * 更新Portfolio与PortfolioInfo关联记录
     *
     * @param request 更新参数
     * @return 更新结果
     */
    @PutMapping(value = "/portfolioInfoUpdate")
    @Log(title = "更新Portfolio与PortfolioInfo关联记录", businessType = BusinessType.UPDATE)
    public R<Long> portfolioInfoUpdate(@RequestBody PortfolioWithInfoRequest request) {
        if (request.getId() == null) {
            return R.fail("Portfolio ID不能为空");
        }

        try {
            Long portfolioId = portfolioService.portfolioInfoUpdate(request);
            return R.ok(portfolioId, "更新Portfolio与PortfolioInfo关联记录成功");
        } catch (Exception e) {
            return R.fail("更新Portfolio与PortfolioInfo关联记录失败：" + e.getMessage());
        }
    }

    /**
     * 删除Portfolio与PortfolioInfo关联记录
     *
     * @param ids Portfolio ID数组
     * @return 删除结果
     */
    @DeleteMapping(value = "/portfolioInfoDelete/{ids}")
    @Log(title = "删除Portfolio与PortfolioInfo关联记录", businessType = BusinessType.DELETE)
    public R<?> portfolioInfoDelete(@PathVariable("ids") Long[] ids) {
        if (ids.length == 0) {
            return R.fail("请选择要删除的记录");
        }

        try {
            Integer count = portfolioService.portfolioInfoDelete(ids);
            return R.ok(count, "删除Portfolio与PortfolioInfo关联记录成功");
        } catch (Exception e) {
            return R.fail("删除Portfolio与PortfolioInfo关联记录失败：" + e.getMessage());
        }
    }
}
