package com.youying.web.controller;

import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.youying.common.annotation.Log;
import com.youying.common.core.common.PullResponse;
import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.Theater;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.common.enums.Enums.AuditFlag;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.common.utils.WechatUtil;
import com.youying.common.utils.file.FileUploadUtils;
import com.youying.system.domain.common.AuditRequest;
import com.youying.system.domain.common.PullRequest;
import com.youying.system.domain.theater.TheaterRequest;
import com.youying.system.domain.theater.TheaterResponse;
import com.youying.system.service.TheaterService;

import lombok.extern.slf4j.Slf4j;

/**
 * 剧场表
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@Slf4j
@RestController
@RequestMapping("/theater")
public class TheaterController extends BaseController {

    @Autowired
    private TheaterService theaterService;

    /**
     * 查询剧场表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<TheaterResponse>> listByPage(@RequestBody TheaterRequest request) {
        startPage(request);
        return R.ok(getTableList(theaterService.listByPage(request)));
    }

    /**
     * 查询剧场表详情(部分)
     *
     * @return
     */
    @GetMapping(value = "/findTheaterInfo")
    public R<TheaterResponse> findTheaterInfo(Long id) {
        return R.ok(theaterService.findTheaterInfo(id));
    }

    /**
     * 查询剧场表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<Theater> details(Long id) {
        return R.ok(theaterService.getById(id));
    }

    /**
     * 修改剧场推荐状态
     *
     * @return
     */
    @PutMapping(value = "/updateRecommend/{id}")
    @Log(title = "修改剧场推荐状态", businessType = BusinessType.UPDATE)
    public R<?> updateRecommend(@PathVariable("id") Long id) {
        return R.ok(theaterService.updateRecommend(id));
    }

    /**
     * 修改剧场启用状态
     *
     * @return
     */
    @PutMapping(value = "/updateStatus/{id}")
    @Log(title = "修改剧场启用状态", businessType = BusinessType.UPDATE)
    public R<?> updateStatus(@PathVariable("id") Long id) {
        Theater theater = theaterService.getById(id);
        Integer status = StatusFlag.OK.getCode().equals(theater.getStatus()) ? StatusFlag.PROHIBITION.getCode()
                : StatusFlag.OK.getCode();
        theater.setStatus(status);
        theaterService.updateById(theater);
        return R.ok();
    }

    /**
     * 剧场审核
     *
     * @return
     */
    @PutMapping(value = "/audit")
    @Log(title = "剧场审核", businessType = BusinessType.UPDATE)
    public R<?> audit(@RequestBody AuditRequest request) {
        Theater theater = theaterService.getById(request.getId());
        if (AuditFlag.PASS.getCode().equals(theater.getAudit())) {
            return R.fail("请勿重复审核");
        }
        if (AuditFlag.PASS.getCode().equals(request.getAudit())) {
            theater.setAuditPassTime(new Date());
            theater.setReasonsRejection(null);
            theater.setStatus(StatusFlag.OK.getCode());
            theater.setShortName(request.getShortName());
            if (AuditFlag.PASS.getCode().equals(request.getAudit())
                    && AuditFlag.WAIT.getCode().equals(theater.getAuditFlag())) {
                theater.setAuditFlag(request.getAudit());
                // 生成二维码
                try {
                    String uploadUrl = FileUploadUtils
                            .upload(WechatUtil.sendWechatQr("pages/sub/detail/theater", theater.getId()));
                    theater.setQrCode(uploadUrl);
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        theater.setStatus(StatusFlag.PROHIBITION.getCode());
        theater.setAudit(request.getAudit());
        theater.setReasonsRejection(request.getReasonsRejection());
        theaterService.updateById(theater);
        return R.ok();
    }

    /**
     * 删除剧场表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除剧场表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(theaterService.delete(Arrays.asList(ids)));
        }
        return R.ok();
    }

    /**
     * 剧场下拉
     *
     * @return
     */
    @PostMapping(value = "/pull")
    public R<List<PullResponse>> pull(@RequestBody PullRequest request) {
        return R.ok(theaterService.pull(request));
    }

    /**
     * 首页-查询剧场添加数量
     *
     * @return
     */
    @PostMapping(value = "/findTheaterAddCount")
    public R<?> findTheaterAddCount(@RequestBody TimeRequest time) {
        return R.ok(theaterService.findTheaterAddCount(time));
    }

    /**
     * 修改剧场信息
     *
     * @param theater 剧场信息
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改剧场信息", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody Theater theater) {
        if (theater.getId() == null) {
            return R.fail("剧场ID不能为空");
        }

        // 检查剧场是否存在
        Theater existingTheater = theaterService.getById(theater.getId());
        if (existingTheater == null) {
            return R.fail("剧场不存在");
        }

        // 更新修改时间
        theater.setUpdateTime(new Date());

        boolean result = theaterService.updateById(theater);
        if (result) {
            return R.ok("修改成功");
        } else {
            return R.fail("修改失败");
        }
    }

}
