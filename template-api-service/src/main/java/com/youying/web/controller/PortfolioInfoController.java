package com.youying.web.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.youying.common.annotation.Log;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.PortfolioInfo;
import com.youying.common.enums.BusinessType;
import com.youying.system.service.PortfolioInfoService;

@RestController
@RequestMapping("/portfolioInfo")
public class PortfolioInfoController {

    @Autowired
    private PortfolioInfoService portfolioInfoService;

    /**
     * 修改藏品组合
     *
     * @return
     */
    @PutMapping(value = "/updateCommonImageByPortfolioId")
    @Log(title = "修改藏品信息", businessType = BusinessType.UPDATE)
    public R<?> updateCommonImageByPortfolioId(@RequestBody PortfolioInfo portfolioInfo) {
        portfolioInfoService.updateCommonImageByPortfolioId(portfolioInfo);
        return R.ok();
    }
}
