# 测试环境配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
  redis:
    # 使用嵌入式Redis或者禁用Redis（用于测试）
    host: localhost
    port: 6379
    database: 0
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0

# 数据库变更监控配置（测试环境）
database:
  change:
    monitor:
      enabled: true
      monitored-tables:
        - t_repertoire
        - t_theater
      async-enabled: false  # 测试环境使用同步处理
      verbose-logging: true  # 开启详细日志便于调试

# 日志配置
logging:
  level:
    com.youying: DEBUG
    org.springframework: WARN
    root: INFO
