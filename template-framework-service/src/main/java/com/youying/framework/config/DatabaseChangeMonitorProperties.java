package com.youying.framework.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Arrays;
import java.util.List;

/**
 * 数据库变更监控配置属性
 * 
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "database.change.monitor")
public class DatabaseChangeMonitorProperties {

    /**
     * 是否启用数据库变更监控
     */
    private boolean enabled = true;

    /**
     * 需要监控的表名列表
     */
    private List<String> monitoredTables = Arrays.asList("t_repertoire", "t_theater");

    /**
     * 是否启用异步处理
     */
    private boolean asyncEnabled = true;

    /**
     * 异步处理线程池大小
     */
    private int asyncThreadPoolSize = 5;

    /**
     * 是否记录详细日志
     */
    private boolean verboseLogging = false;

    /**
     * 缓存刷新超时时间（毫秒）
     */
    private long cacheRefreshTimeout = 5000L;
}
