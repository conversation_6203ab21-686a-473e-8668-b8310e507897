package com.youying.framework.config;

import com.youying.framework.interceptor.DatabaseChangeInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 数据库变更监控配置类
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
@EnableAsync
@EnableConfigurationProperties(DatabaseChangeMonitorProperties.class)
@ConditionalOnProperty(prefix = "database.change.monitor", name = "enabled", havingValue = "true", matchIfMissing = true)
public class DatabaseChangeMonitorConfig {

    /**
     * 注册数据库变更拦截器
     */
    @Bean
    public DatabaseChangeInterceptor databaseChangeInterceptor(DatabaseChangeMonitorProperties properties) {
        log.info("Registering DatabaseChangeInterceptor with monitored tables: {}", 
                properties.getMonitoredTables());
        return new DatabaseChangeInterceptor();
    }
}
