package com.youying.framework.interceptor;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Plugin;
import org.apache.ibatis.plugin.Signature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import com.youying.common.event.DatabaseChangeEvent;
import com.youying.common.utils.spring.SpringUtils;
import com.youying.framework.config.DatabaseChangeMonitorProperties;

import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.delete.Delete;
import net.sf.jsqlparser.statement.insert.Insert;
import net.sf.jsqlparser.statement.update.Update;

/**
 * 数据库变更拦截器
 * 拦截MyBatis-Plus的SQL执行，监控INSERT、UPDATE、DELETE操作并发布事件
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Intercepts({
        @Signature(type = Executor.class, method = "update", args = { MappedStatement.class, Object.class })
})
public class DatabaseChangeInterceptor implements Interceptor {

    /**
     * 需要监控的表名集合（可通过配置文件配置）
     */
    private Set<String> monitoredTables = new HashSet<>(Arrays.asList(
            "t_repertoire", "t_theater"));

    @Autowired(required = false)
    private DatabaseChangeMonitorProperties properties;

    /**
     * 表名提取正则表达式
     */
    private static final Pattern TABLE_NAME_PATTERN = Pattern.compile(
            "(?i)(?:insert\\s+into|update|delete\\s+from)\\s+([a-zA-Z_][a-zA-Z0-9_]*)",
            Pattern.CASE_INSENSITIVE);

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        // 执行原始方法
        Object result = invocation.proceed();

        try {
            MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
            Object parameter = invocation.getArgs()[1];
            SqlCommandType sqlCommandType = mappedStatement.getSqlCommandType();

            // 只处理INSERT、UPDATE、DELETE操作
            if (sqlCommandType == SqlCommandType.INSERT ||
                    sqlCommandType == SqlCommandType.UPDATE ||
                    sqlCommandType == SqlCommandType.DELETE) {

                String sql = getSql(mappedStatement, parameter);
                String tableName = extractTableName(sql);

                // 只监控指定的表
                if (tableName != null && getMonitoredTables().contains(tableName.toLowerCase())) {
                    publishDatabaseChangeEvent(tableName, sqlCommandType, parameter, sql);
                }
            }
        } catch (Exception e) {
            log.error("Error in DatabaseChangeInterceptor: {}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 获取执行的SQL语句
     */
    private String getSql(MappedStatement mappedStatement, Object parameter) {
        try {
            return mappedStatement.getBoundSql(parameter).getSql();
        } catch (Exception e) {
            log.debug("Failed to get SQL from MappedStatement: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 从SQL中提取表名
     */
    private String extractTableName(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return null;
        }

        try {
            // 使用JSQLParser解析SQL
            Statement statement = CCJSqlParserUtil.parse(sql);
            if (statement instanceof Insert) {
                return ((Insert) statement).getTable().getName();
            } else if (statement instanceof Update) {
                return ((Update) statement).getTable().getName();
            } else if (statement instanceof Delete) {
                return ((Delete) statement).getTable().getName();
            }
        } catch (JSQLParserException e) {
            log.debug("Failed to parse SQL with JSQLParser, using regex: {}", e.getMessage());
        }

        // 如果JSQLParser失败，使用正则表达式
        Matcher matcher = TABLE_NAME_PATTERN.matcher(sql);
        if (matcher.find()) {
            return matcher.group(1);
        }

        return null;
    }

    /**
     * 发布数据库变更事件
     */
    private void publishDatabaseChangeEvent(String tableName, SqlCommandType sqlCommandType, Object parameter,
            String sql) {
        try {
            ApplicationEventPublisher eventPublisher = SpringUtils.getBean(ApplicationEventPublisher.class);
            if (eventPublisher == null) {
                log.debug("ApplicationEventPublisher not available");
                return;
            }

            DatabaseChangeEvent.OperationType operationType = convertSqlCommandType(sqlCommandType);
            Object primaryKey = extractPrimaryKey(parameter);

            DatabaseChangeEvent event = new DatabaseChangeEvent(
                    this, tableName, operationType, primaryKey, null, null);

            eventPublisher.publishEvent(event);
            log.debug("Published database change event: {}", event);

        } catch (Exception e) {
            log.error("Error publishing database change event: {}", e.getMessage(), e);
        }
    }

    /**
     * 转换SQL命令类型
     */
    private DatabaseChangeEvent.OperationType convertSqlCommandType(SqlCommandType sqlCommandType) {
        switch (sqlCommandType) {
            case INSERT:
                return DatabaseChangeEvent.OperationType.INSERT;
            case UPDATE:
                return DatabaseChangeEvent.OperationType.UPDATE;
            case DELETE:
                return DatabaseChangeEvent.OperationType.DELETE;
            default:
                throw new IllegalArgumentException("Unsupported SQL command type: " + sqlCommandType);
        }
    }

    /**
     * 提取主键值
     */
    private Object extractPrimaryKey(Object parameter) {
        if (parameter == null) {
            return null;
        }

        try {
            // 如果是实体对象，尝试获取id字段
            if (parameter.getClass().getPackage() != null &&
                    parameter.getClass().getPackage().getName().contains("com.youying")) {
                return getFieldValue(parameter, "id");
            }

            // 如果是Map，尝试获取id或主键
            if (parameter instanceof Map) {
                Map<?, ?> map = (Map<?, ?>) parameter;
                Object id = map.get("id");
                if (id != null) {
                    return id;
                }
                // 尝试其他可能的主键名称
                for (String key : Arrays.asList("ID", "Id", "primaryKey", "pk")) {
                    Object value = map.get(key);
                    if (value != null) {
                        return value;
                    }
                }
            }
        } catch (Exception e) {
            log.debug("Failed to extract primary key: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 通过反射获取字段值
     */
    private Object getFieldValue(Object obj, String fieldName) {
        try {
            java.lang.reflect.Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(obj);
        } catch (Exception e) {
            log.debug("Failed to get field value for {}: {}", fieldName, e.getMessage());
            return null;
        }
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    /**
     * 获取需要监控的表名集合
     */
    private Set<String> getMonitoredTables() {
        if (properties != null && properties.getMonitoredTables() != null
                && !properties.getMonitoredTables().isEmpty()) {
            return new HashSet<>(properties.getMonitoredTables());
        }
        return monitoredTables;
    }

    @Override
    public void setProperties(Properties properties) {
        // 可以从配置文件中读取监控的表名
        String tables = properties.getProperty("monitoredTables");
        if (tables != null && !tables.trim().isEmpty()) {
            monitoredTables.clear();
            String[] tableArray = tables.split(",");
            for (String table : tableArray) {
                monitoredTables.add(table.trim().toLowerCase());
            }
            log.info("Configured monitored tables: {}", monitoredTables);
        }
    }
}
