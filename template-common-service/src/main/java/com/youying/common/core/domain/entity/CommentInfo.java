package com.youying.common.core.domain.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 评论点赞、踩详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_comment_info")
public class CommentInfo extends Model<CommentInfo> {

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 评论ID
     */
    @TableField("comment_id")
    private Long commentId;

    /**
     * 赞或踩（1赞，0踩）
     */
    @TableField("`type`")
    private Integer type;

    @Override
    public Serializable pkVal() {
        return null;
    }

}
