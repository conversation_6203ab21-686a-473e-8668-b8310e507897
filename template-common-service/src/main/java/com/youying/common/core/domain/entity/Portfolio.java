package com.youying.common.core.domain.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 藏品组合表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-25
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_portfolio")
public class Portfolio extends Model<Portfolio> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * ocr编号
     */
    @TableField("ocr_no")
    private Long ocrNo;

    /**
     * 腾讯识别ID
     */
    @TableField("scanning_id")
    private Long scanningId;

    /**
     * 剧目剧场关联编号
     */
    @TableField("`no`")
    private String no;

    /**
     * 票类型
     */
    @TableField("ticket_type")
    private Integer ticketType;

    /**
     * 组合名称
     */
    @TableField("`name`")
    private String name;

    /**
     * 批次
     */
    @TableField("batch")
    private Integer batch;

    /**
     * 商家ID
     */
    @TableField("merchant_id")
    private Long merchantId;

    /**
     * 剧场ID
     */
    @TableField("theater_id")
    private Long theaterId;

    /**
     * 剧目ID
     */
    @TableField("repertoire_id")
    private Long repertoireId;

    /**
     * 电子票ID
     */
    @TableField("repertoire_ticket_id")
    private Long repertoireTicketId;

    /**
     * 数字头像ID
     */
    @TableField("digital_avatar_id")
    private Long digitalAvatarId;

    /**
     * 组合介绍
     */
    @TableField("introduction")
    private String introduction;

    /**
     * 发放数量
     */
    @TableField("issued_quantity")
    private Integer issuedQuantity;

    /**
     * 发放时间
     */
    @TableField("start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 免费发放
     */
    @TableField("free")
    private Integer free;

    /**
     * 价格
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * 免责声明
     */
    @TableField("portfolio_statement_id")
    private Long portfolioStatementId;

    /**
     * 免责声明
     */
    @TableField("statement")
    private String statement;

    /**
     * 座位是否可重复
     */
    @TableField("seat_status")
    private Integer seatStatus;

    /**
     * 橱窗展示状态
     */
    @TableField("`look_status`")
    private Integer lookStatus;

    /**
     * 售罄状态
     */
    @TableField("sold_out")
    private Integer soldOut;

    /**
     * 审核（0待审核，1驳回，2通过）
     */
    @TableField("audit")
    private Integer audit;

    /**
     * 审核通过
     */
    @TableField("audit_flag")
    private Integer auditFlag;

    /**
     * 审核通过时间
     */
    @TableField("audit_pass_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditPassTime;

    /**
     * 修改原因
     */
    @TableField("update_cause")
    private String updateCause;

    /**
     * 驳回原因
     */
    @TableField("reasons_rejection")
    private String reasonsRejection;

    /**
     * 是否删除
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * 状态
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 电子票的中间位置是否加入剧目名称
     */
    @TableField(value = "if_composite_repertoire_name")
    private Boolean ifCompositeRepertoireName;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
