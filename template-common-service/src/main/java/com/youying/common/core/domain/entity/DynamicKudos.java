package com.youying.common.core.domain.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 剧场动态点赞表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-14
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_dynamic_kudos")
public class DynamicKudos extends Model<DynamicKudos> {

    /**
     * 动态ID
     */
    @TableField("dynamic_id")
    private Long dynamicId;

    /**
     * 赞或踩（1赞，0踩）
     */
    @TableField("`type`")
    private Integer type;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    @Override
    public Serializable pkVal() {
        return null;
    }

}
