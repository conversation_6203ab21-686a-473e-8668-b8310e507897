package com.youying.common.constant;

/**
 * 缓存的key 常量
 *
 * <AUTHOR>
 */
public class CacheConstants {
    /**
     * 登录用户 redis key
     */
    public static final String LOGIN_TOKEN_KEY = "login_tokens:";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "sys_dict:";

    /**
     * 防重提交 redis key
     */
    public static final String REPEAT_SUBMIT_KEY = "repeat_submit:";

    /**
     * 限流 redis key
     */
    public static final String RATE_LIMIT_KEY = "rate_limit:";

    /**
     * 登录账户密码错误次数 redis key
     */
    public static final String PWD_ERR_CNT_KEY = "pwd_err_cnt:";

    /**
     * 验证码登录前缀
     */
    public static final String PHONE_CODE_LOGIN_KEY = "login:phone_code:";

    /**
     * 剧目相关缓存 redis key
     */
    public static final String REPERTOIRE_CACHE_KEY = "repertoire:";

    /**
     * 剧目列表缓存 redis key
     */
    public static final String REPERTOIRE_LIST_CACHE_KEY = "repertoire:list:";

    /**
     * 剧目详情缓存 redis key
     */
    public static final String REPERTOIRE_INFO_CACHE_KEY = "repertoire:info:";

    /**
     * 剧场相关缓存 redis key
     */
    public static final String THEATER_CACHE_KEY = "theater:";

    /**
     * 剧场列表缓存 redis key
     */
    public static final String THEATER_LIST_CACHE_KEY = "theater:list:";

    /**
     * 剧场详情缓存 redis key
     */
    public static final String THEATER_INFO_CACHE_KEY = "theater:info:";
}
