# 数据库变更监控和Redis缓存刷新系统

## 🎯 功能概述

本系统实现了基于Spring Boot的数据库变更监控和Redis缓存自动刷新功能。当监控的数据库表（如`t_repertoire`、`t_theater`）发生INSERT、UPDATE、DELETE操作时，系统会自动清理相关的Redis缓存，确保数据一致性。

## ✨ 核心特性

- **🔍 自动监控**：基于MyBatis拦截器自动监控SQL执行
- **🚀 异步处理**：支持异步缓存刷新，不影响主业务性能
- **🔧 模块化设计**：易于扩展，支持新增表的监控
- **⚙️ 配置灵活**：通过配置文件控制监控行为
- **📊 完善日志**：详细的操作日志，便于调试和监控
- **🎛️ 手动控制**：提供API接口手动触发缓存刷新

## 🏗️ 系统架构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   SQL执行       │───▶│  MyBatis拦截器   │───▶│  发布变更事件    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   清理Redis缓存  │◀───│  执行刷新策略     │◀───│ 缓存刷新管理器   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 📁 文件结构

```
├── template-common-service/
│   ├── src/main/java/com/youying/common/
│   │   ├── event/DatabaseChangeEvent.java              # 数据库变更事件
│   │   ├── cache/CacheRefreshStrategy.java             # 缓存刷新策略接口
│   │   ├── cache/CacheRefreshManager.java              # 缓存刷新管理器
│   │   └── constant/CacheConstants.java                # 缓存常量定义
├── template-framework-service/
│   └── src/main/java/com/youying/framework/
│       ├── interceptor/DatabaseChangeInterceptor.java  # MyBatis拦截器
│       └── config/DatabaseChangeMonitorConfig.java     # 自动配置类
├── template-server-service/
│   └── src/main/java/com/youying/system/cache/
│       ├── RepertoireCacheRefreshStrategy.java         # 剧目缓存刷新策略
│       ├── TheaterCacheRefreshStrategy.java            # 剧场缓存刷新策略
│       └── ExampleCacheRefreshStrategy.java            # 示例缓存刷新策略
├── template-api-service/
│   └── src/main/java/com/youying/web/controller/system/
│       └── CacheRefreshController.java                 # 缓存刷新控制器
└── docs/
    └── database-change-monitor.md                      # 详细文档
```

## 🚀 快速开始

### 1. 配置启用

在`application.yml`中添加配置：

```yaml
database:
  change:
    monitor:
      enabled: true
      monitored-tables:
        - t_repertoire
        - t_theater
      async-enabled: true
```

### 2. 系统自动工作

配置完成后，系统会自动监控指定表的变更并刷新相关缓存。

### 3. 手动触发（可选）

通过API接口手动触发缓存刷新：

```bash
# 获取支持的表名
GET /system/cache/refresh/tables

# 手动刷新剧目缓存
POST /system/cache/refresh/repertoire/123?operationType=UPDATE

# 手动刷新剧场缓存
POST /system/cache/refresh/theater/456?operationType=UPDATE
```

## 🔧 为新表添加监控

### 步骤1：更新配置

```yaml
database:
  change:
    monitor:
      monitored-tables:
        - t_repertoire
        - t_theater
        - t_your_new_table  # 添加新表
```

### 步骤2：实现缓存刷新策略

```java
@Component
public class YourTableCacheRefreshStrategy implements CacheRefreshStrategy {
    
    @Override
    public String getSupportedTableName() {
        return "t_your_new_table";
    }
    
    @Override
    public void refreshCache(DatabaseChangeEvent event) {
        // 实现具体的缓存清理逻辑
    }
}
```

### 步骤3：添加缓存常量

```java
public static final String YOUR_TABLE_CACHE_KEY = "your_table:";
```

## 📊 监控和日志

系统提供详细的日志记录：

```
2024-01-01 10:00:00 INFO  - Published database change event: t_repertoire, UPDATE, primaryKey=123
2024-01-01 10:00:00 INFO  - Processing database change event for table: t_repertoire, operation: UPDATE, primaryKey: 123
2024-01-01 10:00:00 INFO  - Successfully refreshed repertoire cache for primaryKey: 123
```

## 🎯 已实现的缓存策略

### Repertoire表（剧目）
- 详情缓存：`repertoire:info:{id}`
- 列表缓存：`repertoire:list:*`
- 推荐缓存：`repertoire:recommend:list`
- 分类缓存：`repertoire:category:*`

### Theater表（剧场）
- 详情缓存：`theater:info:{id}`
- 列表缓存：`theater:list:*`
- 推荐缓存：`theater:recommend:list`
- 地区缓存：`theater:area:*`

## ⚠️ 注意事项

1. **性能影响**：缓存刷新默认异步执行，对主业务无影响
2. **异常处理**：缓存刷新失败不会影响数据库操作
3. **配置灵活**：可通过配置文件控制监控范围和行为
4. **扩展性强**：新增表监控只需实现策略接口

## 🔍 故障排除

### 常见问题

1. **事件未触发**
   - 检查表名是否在`monitored-tables`配置中
   - 确认`enabled: true`

2. **缓存未清理**
   - 检查缓存刷新策略是否正确实现
   - 查看日志确认策略是否执行

3. **性能问题**
   - 调整`async-thread-pool-size`
   - 优化缓存清理逻辑

### 调试方法

1. 开启详细日志：`verbose-logging: true`
2. 查看事件发布日志
3. 监控缓存刷新执行情况

## 📚 更多文档

- [详细使用文档](docs/database-change-monitor.md)
- [API接口文档](http://localhost:8110/swagger-ui/)

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个系统！
