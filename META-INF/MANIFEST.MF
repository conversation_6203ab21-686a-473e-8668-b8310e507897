Manifest-Version: 1.0
Created-By: <PERSON><PERSON> 3.2.0
Build-Jdk-Spec: 17
Class-Path: lib/ lib/spring-boot-devtools-2.5.14.jar lib/spring-boot-2.5
 .14.jar lib/spring-core-5.3.20.jar lib/spring-jcl-5.3.20.jar lib/spring
 -context-5.3.20.jar lib/spring-expression-5.3.20.jar lib/spring-boot-au
 toconfigure-2.5.14.jar lib/druid-spring-boot-starter-1.2.16.jar lib/dru
 id-1.2.16.jar lib/slf4j-api-1.7.36.jar lib/springfox-boot-starter-3.0.0
 .jar lib/springfox-oas-3.0.0.jar lib/swagger-annotations-2.1.2.jar lib/
 swagger-models-2.1.2.jar lib/springfox-spi-3.0.0.jar lib/springfox-sche
 ma-3.0.0.jar lib/springfox-core-3.0.0.jar lib/byte-buddy-1.10.22.jar li
 b/springfox-spring-web-3.0.0.jar lib/classgraph-4.8.83.jar lib/springfo
 x-spring-webmvc-3.0.0.jar lib/springfox-spring-webflux-3.0.0.jar lib/sp
 ringfox-swagger-common-3.0.0.jar lib/mapstruct-1.3.1.Final.jar lib/spri
 ngfox-data-rest-3.0.0.jar lib/springfox-bean-validators-3.0.0.jar lib/s
 pringfox-swagger2-3.0.0.jar lib/springfox-swagger-ui-3.0.0.jar lib/clas
 smate-1.5.1.jar lib/spring-plugin-core-2.0.0.RELEASE.jar lib/spring-bea
 ns-5.3.20.jar lib/spring-aop-5.3.20.jar lib/spring-plugin-metadata-2.0.
 0.RELEASE.jar lib/swagger-models-1.6.2.jar lib/jackson-annotations-2.12
 .6.jar lib/swagger-annotations-1.6.2.jar lib/mysql-connector-java-8.0.2
 9.jar lib/template-framework-service-1.0.0.jar lib/spring-boot-starter-
 web-2.5.14.jar lib/spring-boot-starter-json-2.5.14.jar lib/jackson-data
 bind-2.12.6.1.jar lib/jackson-core-2.12.6.jar lib/jackson-datatype-jdk8
 -2.12.6.jar lib/jackson-datatype-jsr310-2.12.6.jar lib/jackson-module-p
 arameter-names-2.12.6.jar lib/spring-boot-starter-tomcat-2.5.14.jar lib
 /tomcat-embed-core-9.0.63.jar lib/tomcat-embed-el-9.0.63.jar lib/tomcat
 -embed-websocket-9.0.63.jar lib/spring-web-5.3.20.jar lib/spring-webmvc
 -5.3.20.jar lib/spring-boot-starter-aop-2.5.14.jar lib/aspectjweaver-1.
 9.7.jar lib/mybatis-plus-boot-starter-3.5.3.jar lib/mybatis-plus-3.5.3.
 jar lib/mybatis-plus-extension-3.5.3.jar lib/mybatis-plus-core-3.5.3.ja
 r lib/mybatis-plus-annotation-3.5.3.jar lib/jsqlparser-4.4.jar lib/myba
 tis-3.5.10.jar lib/mybatis-spring-2.0.7.jar lib/spring-boot-starter-jdb
 c-2.5.14.jar lib/HikariCP-4.0.3.jar lib/spring-jdbc-5.3.20.jar lib/spri
 ng-tx-5.3.20.jar lib/kaptcha-2.3.3.jar lib/filters-2.0.235-1.jar lib/se
 rvlet-api-2.5.jar lib/oshi-core-6.4.0.jar lib/jna-5.12.1.jar lib/jna-pl
 atform-5.12.1.jar lib/template-server-service-1.0.0.jar lib/template-co
 mmon-service-1.0.0.jar lib/spring-context-support-5.3.20.jar lib/spring
 -test-5.3.20.jar lib/spring-boot-starter-security-2.5.14.jar lib/spring
 -security-config-5.5.8.jar lib/spring-security-core-5.5.8.jar lib/sprin
 g-security-crypto-5.5.8.jar lib/spring-security-web-5.5.8.jar lib/pageh
 elper-spring-boot-starter-1.4.6.jar lib/mybatis-spring-boot-starter-2.2
 .2.jar lib/mybatis-spring-boot-autoconfigure-2.2.2.jar lib/pagehelper-s
 pring-boot-autoconfigure-1.4.6.jar lib/pagehelper-5.3.2.jar lib/spring-
 boot-starter-validation-2.5.14.jar lib/hibernate-validator-6.2.3.Final.
 jar lib/jakarta.validation-api-2.0.2.jar lib/jboss-logging-3.4.3.Final.
 jar lib/commons-lang3-3.12.0.jar lib/dynamic-datasource-spring-boot-sta
 rter-3.5.2.jar lib/fastjson2-2.0.25.jar lib/commons-io-2.11.0.jar lib/p
 oi-ooxml-4.1.2.jar lib/poi-4.1.2.jar lib/commons-collections4-4.4.jar l
 ib/commons-math3-3.6.1.jar lib/SparseBitSet-1.2.jar lib/poi-ooxml-schem
 as-4.1.2.jar lib/xmlbeans-3.1.0.jar lib/commons-compress-1.19.jar lib/c
 urvesapi-1.06.jar lib/jjwt-0.9.1.jar lib/jaxb-api-2.3.1.jar lib/javax.a
 ctivation-api-1.2.0.jar lib/spring-boot-starter-data-redis-2.5.14.jar l
 ib/spring-data-redis-2.5.11.jar lib/spring-data-keyvalue-2.5.11.jar lib
 /spring-data-commons-2.5.11.jar lib/spring-oxm-5.3.20.jar lib/lettuce-c
 ore-6.1.8.RELEASE.jar lib/netty-common-4.1.77.Final.jar lib/netty-handl
 er-4.1.77.Final.jar lib/netty-resolver-4.1.77.Final.jar lib/netty-buffe
 r-4.1.77.Final.jar lib/netty-codec-4.1.77.Final.jar lib/netty-transport
 -4.1.77.Final.jar lib/reactor-core-3.4.18.jar lib/reactive-streams-1.0.
 3.jar lib/commons-pool2-2.9.0.jar lib/UserAgentUtils-1.21.jar lib/javax
 .servlet-api-4.0.1.jar lib/lombok-1.18.24.jar lib/spring-boot-starter-w
 ebsocket-2.5.14.jar lib/spring-messaging-5.3.20.jar lib/spring-websocke
 t-5.3.20.jar lib/gson-2.9.1.jar lib/bcpkix-jdk15on-1.57.jar lib/bcprov-
 jdk15on-1.57.jar lib/commons-lang-2.6.jar lib/commons-logging-1.2.jar l
 ib/httpclient-4.5.jar lib/commons-codec-1.15.jar lib/httpcore-4.4.1.jar
  lib/easypoi-spring-boot-starter-4.2.0.jar lib/easypoi-web-4.2.0.jar li
 b/easypoi-base-4.2.0.jar lib/guava-16.0.1.jar lib/ognl-3.2.6.jar lib/ja
 vassist-3.20.0-GA.jar lib/validation-api-2.0.1.Final.jar lib/easypoi-an
 notation-4.2.0.jar lib/hutool-all-5.7.22.jar lib/spring-boot-starter-th
 ymeleaf-2.5.14.jar lib/spring-boot-starter-2.5.14.jar lib/spring-boot-s
 tarter-logging-2.5.14.jar lib/logback-classic-1.2.11.jar lib/logback-co
 re-1.2.11.jar lib/log4j-to-slf4j-2.17.2.jar lib/log4j-api-2.17.2.jar li
 b/jul-to-slf4j-1.7.36.jar lib/jakarta.annotation-api-1.3.5.jar lib/snak
 eyaml-1.28.jar lib/thymeleaf-spring5-3.0.15.RELEASE.jar lib/thymeleaf-3
 .0.15.RELEASE.jar lib/attoparser-2.0.5.RELEASE.jar lib/unbescape-1.1.6.
 RELEASE.jar lib/thymeleaf-extras-java8time-3.0.4.RELEASE.jar
Main-Class: com.youying.ApplicationMain

