package com.youying.system.cache;

import com.youying.common.cache.CacheRefreshManager;
import com.youying.common.event.DatabaseChangeEvent;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

/**
 * 数据库变更监控测试
 * 
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("test")
public class DatabaseChangeMonitorTest {

    @Autowired(required = false)
    private CacheRefreshManager cacheRefreshManager;

    @Test
    public void testGetSupportedTables() {
        if (cacheRefreshManager != null) {
            List<String> tables = cacheRefreshManager.getSupportedTables();
            System.out.println("Supported tables: " + tables);
            
            // 验证是否包含预期的表
            assert tables.contains("t_repertoire") : "Should contain t_repertoire";
            assert tables.contains("t_theater") : "Should contain t_theater";
        } else {
            System.out.println("CacheRefreshManager is not available - this is expected if the feature is disabled");
        }
    }

    @Test
    public void testManualRefresh() {
        if (cacheRefreshManager != null) {
            // 测试手动触发缓存刷新
            cacheRefreshManager.manualRefresh("t_repertoire", 
                DatabaseChangeEvent.OperationType.UPDATE, 123L);
            
            cacheRefreshManager.manualRefresh("t_theater", 
                DatabaseChangeEvent.OperationType.INSERT, 456L);
            
            System.out.println("Manual refresh test completed");
        } else {
            System.out.println("CacheRefreshManager is not available - skipping manual refresh test");
        }
    }

    @Test
    public void testDatabaseChangeEvent() {
        // 测试事件创建
        DatabaseChangeEvent insertEvent = DatabaseChangeEvent.createInsertEvent(
            this, "t_repertoire", 123L, null);
        
        assert insertEvent.getTableName().equals("t_repertoire");
        assert insertEvent.getOperationType() == DatabaseChangeEvent.OperationType.INSERT;
        assert insertEvent.getPrimaryKey().equals(123L);
        
        DatabaseChangeEvent updateEvent = DatabaseChangeEvent.createUpdateEvent(
            this, "t_theater", 456L, null, null);
        
        assert updateEvent.getTableName().equals("t_theater");
        assert updateEvent.getOperationType() == DatabaseChangeEvent.OperationType.UPDATE;
        assert updateEvent.getPrimaryKey().equals(456L);
        
        DatabaseChangeEvent deleteEvent = DatabaseChangeEvent.createDeleteEvent(
            this, "t_repertoire", 789L, null);
        
        assert deleteEvent.getTableName().equals("t_repertoire");
        assert deleteEvent.getOperationType() == DatabaseChangeEvent.OperationType.DELETE;
        assert deleteEvent.getPrimaryKey().equals(789L);
        
        System.out.println("DatabaseChangeEvent test completed");
    }
}
