package com.youying.system.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.DigitalAvatar;
import com.youying.common.core.domain.entity.Portfolio;
import com.youying.common.core.domain.entity.PortfolioInfo;
import com.youying.common.core.domain.entity.RepertoireTicket;
import com.youying.common.enums.Enums.AuditFlag;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.common.exception.ServiceException;
import com.youying.common.utils.StringUtils;
import com.youying.system.domain.portfolio.PortfolioRequest;
import com.youying.system.domain.portfolio.PortfolioResponse;
import com.youying.system.domain.portfolio.PortfolioWithInfoRequest;
import com.youying.system.domain.portfolio.PortfolioWithInfoResponse;
import com.youying.system.mapper.PortfolioMapper;
import com.youying.system.service.DigitalAvatarService;
import com.youying.system.service.PortfolioInfoService;
import com.youying.system.service.PortfolioService;
import com.youying.system.service.RepertoireTicketService;
import com.youying.system.service.UserReceivingRecordsService;

/**
 * <p>
 * 藏品组合表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
@Service
public class PortfolioServiceImpl extends ServiceImpl<PortfolioMapper, Portfolio> implements PortfolioService {
    @Autowired
    private UserReceivingRecordsService userReceivingRecordsService;
    @Autowired
    private PortfolioInfoService portfolioInfoService;
    @Autowired
    private RepertoireTicketService repertoireTicketService;
    @Autowired
    private DigitalAvatarService digitalAvatarService;

    /**
     * 藏品组合表列表
     *
     * @param request
     * @return
     */
    @Override
    public List<PortfolioResponse> listByPage(PortfolioRequest request) {
        return baseMapper.listByPage(request);
    }

    /**
     * 删除藏品组合
     *
     * @param ids
     * @return
     */
    @Override
    @Transactional
    public Integer delete(Long[] ids) {
        // 判断是否
        Long count = userReceivingRecordsService.findPortfolioCountById(ids);
        if (count > 0) {
            throw new ServiceException("已有用户领取，无法删除");
        }
        removeBatchByIds(Arrays.asList(ids));
        return ids.length;
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @Override
    public PortfolioResponse details(Long id) {
        return baseMapper.details(id);
    }

    /**
     * 商品审核
     *
     * @param portfolio
     * @return
     */
    @Override
    @Transactional
    public Long audit(Portfolio portfolio) {
        Portfolio portfolioInfo = getById(portfolio.getId());
        if (AuditFlag.PASS.getCode().equals(portfolio.getAudit())) {
            portfolioInfo.setAudit(AuditFlag.PASS.getCode());
            portfolioInfo.setAuditFlag(AuditFlag.PASS.getCode());
            portfolioInfo.setAuditPassTime(new Date());

            DigitalAvatar digitalAvatar = digitalAvatarService.getById(portfolioInfo.getDigitalAvatarId());
            RepertoireTicket repertoireTicket = repertoireTicketService.getById(portfolioInfo.getRepertoireTicketId());

            // 添加商品审核记录
            portfolioInfoService.updateStatus(portfolioInfo.getId());

            // 添加商品审核记录
            PortfolioInfo portfolioInfoDetail = new PortfolioInfo();
            BeanUtils.copyProperties(portfolioInfo, portfolioInfoDetail);
            portfolioInfoDetail.setId(null);
            portfolioInfoDetail.setPortfolioId(portfolioInfo.getId());
            if (repertoireTicket != null) {
                portfolioInfoDetail.setCoverFront(repertoireTicket.getCoverFront());
                portfolioInfoDetail.setCoverReverse(repertoireTicket.getCoverReverse());
                portfolioInfoDetail.setCommonImage(repertoireTicket.getCommonImage());

            }
            if (digitalAvatar != null) {
                if (StringUtils.isNotBlank(repertoireTicket.getCoverFront())
                        && digitalAvatar.getMaxQuantity() < portfolioInfo.getIssuedQuantity() - 1) {
                    throw new ServiceException("发放数量超出数字头像阈值");
                }

                portfolioInfoDetail.setDigitalAvatarCommonImage(digitalAvatar.getCommonImage());
            }
            portfolioInfoDetail.setStatus(StatusFlag.OK.getCode());
            portfolioInfoService.save(portfolioInfoDetail);

            // 如果更新了电子票图片，并且审核通过了，重新生成藏品图片
            if (repertoireTicket != null && portfolio.getAudit().equals(AuditFlag.PASS.getCode())
                    && StringUtils.isNotBlank(repertoireTicket.getCommonImage())) {
                // 异步线程处理
                new Thread(() -> {
                    userReceivingRecordsService.regenerateUserPortfolioImage(portfolioInfo.getId());
                }).start();
            }
        } else {
            // 驳回
            portfolioInfo.setReasonsRejection(portfolio.getReasonsRejection());
            portfolioInfo.setAudit(AuditFlag.REJECTED.getCode());
        }
        portfolioInfo.setPrice(portfolio.getPrice());
        portfolioInfo.setFree(portfolio.getFree());
        portfolioInfo.setPortfolioStatementId(portfolio.getPortfolioStatementId());
        portfolioInfo.setStatement(portfolio.getStatement());

        updateById(portfolioInfo);

        return portfolio.getId();
    }

    /**
     * 修改商品组合
     *
     * @param portfolio
     * @return
     */
    @Override
    @Transactional
    public Long update(Portfolio portfolio) {
        Portfolio portfolioInfo = getById(portfolio.getId());
        portfolioInfo.setOcrNo(portfolio.getOcrNo());
        portfolioInfo.setScanningId(portfolio.getScanningId());
        portfolioInfo.setPrice(portfolio.getPrice());
        portfolioInfo.setStatement(portfolio.getStatement());
        portfolioInfo.setPortfolioStatementId(portfolioInfo.getPortfolioStatementId());
        portfolioInfo.setFree(portfolio.getFree());
        portfolioInfo.setReasonsRejection(portfolio.getReasonsRejection());
        updateById(portfolio);

        PortfolioInfo portfolioInfoDetails = portfolioInfoService.getOne(new LambdaQueryWrapper<PortfolioInfo>()
                .eq(PortfolioInfo::getPortfolioId, portfolio.getId())
                .eq(PortfolioInfo::getStatus, StatusFlag.OK.getCode()));
        if (portfolioInfoDetails != null) {
            portfolioInfoDetails.setOcrNo(portfolio.getOcrNo());
            portfolioInfoDetails.setPrice(portfolio.getPrice());
            portfolioInfoDetails.setStatement(portfolio.getStatement());
            portfolioInfoDetails.setPortfolioStatementId(portfolioInfo.getPortfolioStatementId());
            portfolioInfoDetails.setFree(portfolio.getFree());

            portfolioInfoService.updateById(portfolioInfoDetails);
        }

        return portfolio.getId();
    }

    /**
     * 查询规则使用条数
     *
     * @param scanningId
     * @return
     */
    @Override
    public Long findScanningCount(Long scanningId) {
        return count(new LambdaQueryWrapper<Portfolio>()
                .eq(Portfolio::getScanningId, scanningId));
    }

    // ========== 以下为Portfolio与PortfolioInfo关联的增删查改方法实现 ==========

    /**
     * 新增Portfolio与PortfolioInfo关联记录
     *
     * @param request 请求参数
     * @return 新增记录的Portfolio ID
     */
    @Override
    @Transactional
    public Long portfolioInfoCreate(PortfolioWithInfoRequest request) {
        // 1. 创建Portfolio记录
        Portfolio portfolio = new Portfolio();
        BeanUtils.copyProperties(request, portfolio);

        // 设置默认值

        if (portfolio.getAudit() == null) {
            portfolio.setAudit(AuditFlag.WAIT.getCode()); // 0-待审核
        }
        if (portfolio.getAuditFlag() == null) {
            portfolio.setAuditFlag(AuditFlag.WAIT.getCode()); // 0-待审核
        }
        if (portfolio.getStatus() == null) {
            portfolio.setStatus(StatusFlag.OK.getCode()); // 1-正常状态
        }
        if (portfolio.getSeatStatus() == null) {
            portfolio.setSeatStatus(0); // 默认座位不可重复
        }
        if (portfolio.getLookStatus() == null) {
            portfolio.setLookStatus(0); // 默认不展示
        }
        if (portfolio.getSoldOut() == null) {
            portfolio.setSoldOut(0); // 默认未售罄
        }
        if (portfolio.getFree() == null) {
            portfolio.setFree(0); // 默认非免费
        }
        if (portfolio.getBatch() == null) {
            portfolio.setBatch(1); // 默认第1批次
        }

        // 保存Portfolio
        save(portfolio);
        Long portfolioId = portfolio.getId();

        // 2. 创建PortfolioInfo记录
        PortfolioInfo portfolioInfo = new PortfolioInfo();
        BeanUtils.copyProperties(request, portfolioInfo);
        portfolioInfo.setId(null); // 清空ID，让数据库自动生成
        portfolioInfo.setPortfolioId(portfolioId); // 设置关联的Portfolio ID
        portfolioInfo.setStatus(StatusFlag.OK.getCode()); // 设置状态为正常

        // 获取电子票和数字头像信息
        if (request.getRepertoireTicketId() != null) {
            RepertoireTicket repertoireTicket = repertoireTicketService.getById(request.getRepertoireTicketId());
            if (repertoireTicket != null) {
                portfolioInfo.setCoverFront(repertoireTicket.getCoverFront());
                portfolioInfo.setCoverReverse(repertoireTicket.getCoverReverse());
                portfolioInfo.setCommonImage(repertoireTicket.getCommonImage());
            }
        }

        if (request.getDigitalAvatarId() != null) {
            DigitalAvatar digitalAvatar = digitalAvatarService.getById(request.getDigitalAvatarId());
            if (digitalAvatar != null) {
                portfolioInfo.setDigitalAvatarCommonImage(digitalAvatar.getCommonImage());
            }
        }

        // 保存PortfolioInfo
        portfolioInfoService.save(portfolioInfo);

        return portfolioId;
    }

    /**
     * 根据ID查询Portfolio与PortfolioInfo关联详情
     *
     * @param id Portfolio ID
     * @return 详情信息
     */
    @Override
    public PortfolioWithInfoResponse portfolioInfoDetails(Long id) {
        // 查询Portfolio
        Portfolio portfolio = getById(id);
        if (portfolio == null) {
            return null;
        }

        // 查询对应的PortfolioInfo（状态为正常的）
        PortfolioInfo portfolioInfo = portfolioInfoService.getOne(
                new LambdaQueryWrapper<PortfolioInfo>()
                        .eq(PortfolioInfo::getPortfolioId, id)
                        .eq(PortfolioInfo::getStatus, StatusFlag.OK.getCode()));

        // 组装响应数据
        PortfolioWithInfoResponse response = new PortfolioWithInfoResponse();
        BeanUtils.copyProperties(portfolio, response);

        if (portfolioInfo != null) {
            response.setPortfolioInfoId(portfolioInfo.getId());
            response.setCoverFront(portfolioInfo.getCoverFront());
            response.setCoverReverse(portfolioInfo.getCoverReverse());
            response.setCommonImage(portfolioInfo.getCommonImage());
            response.setDigitalAvatarCommonImage(portfolioInfo.getDigitalAvatarCommonImage());
            response.setPortfolioInfoStatus(portfolioInfo.getStatus());
        }

        return response;
    }

    /**
     * 分页查询Portfolio与PortfolioInfo关联列表
     *
     * @param request 查询条件
     * @return 列表数据
     */
    @Override
    public List<PortfolioWithInfoResponse> portfolioInfoListByPage(PortfolioRequest request) {
        // 查询Portfolio列表
        List<PortfolioResponse> portfolioList = baseMapper.listByPage(request);

        List<PortfolioWithInfoResponse> responseList = new ArrayList<>();

        for (PortfolioResponse portfolioResponse : portfolioList) {
            PortfolioWithInfoResponse response = new PortfolioWithInfoResponse();
            BeanUtils.copyProperties(portfolioResponse, response);

            // 查询对应的PortfolioInfo
            PortfolioInfo portfolioInfo = portfolioInfoService.getOne(
                    new LambdaQueryWrapper<PortfolioInfo>()
                            .eq(PortfolioInfo::getPortfolioId, portfolioResponse.getId())
                            .eq(PortfolioInfo::getStatus, StatusFlag.OK.getCode()));

            if (portfolioInfo != null) {
                response.setPortfolioInfoId(portfolioInfo.getId());
                response.setCoverFront(portfolioInfo.getCoverFront());
                response.setCoverReverse(portfolioInfo.getCoverReverse());
                response.setCommonImage(portfolioInfo.getCommonImage());
                response.setDigitalAvatarCommonImage(portfolioInfo.getDigitalAvatarCommonImage());
                response.setPortfolioInfoStatus(portfolioInfo.getStatus());
            }

            responseList.add(response);
        }

        return responseList;
    }

    /**
     * 更新Portfolio与PortfolioInfo关联记录
     *
     * @param request 更新参数
     * @return 更新记录的Portfolio ID
     */
    @Override
    @Transactional
    public Long portfolioInfoUpdate(PortfolioWithInfoRequest request) {
        if (request.getId() == null) {
            throw new ServiceException("Portfolio ID不能为空");
        }

        // 1. 更新Portfolio记录
        Portfolio portfolio = getById(request.getId());
        if (portfolio == null) {
            throw new ServiceException("Portfolio记录不存在");
        }

        BeanUtils.copyProperties(request, portfolio);
        updateById(portfolio);

        // 2. 处理PortfolioInfo记录
        // 先关闭旧的PortfolioInfo记录
        portfolioInfoService.updateStatus(request.getId());

        // 创建新的PortfolioInfo记录
        PortfolioInfo portfolioInfo = new PortfolioInfo();
        BeanUtils.copyProperties(request, portfolioInfo);
        portfolioInfo.setId(null); // 清空ID，让数据库自动生成
        portfolioInfo.setPortfolioId(request.getId()); // 设置关联的Portfolio ID
        portfolioInfo.setStatus(StatusFlag.OK.getCode()); // 设置状态为正常

        // 获取电子票和数字头像信息
        if (request.getRepertoireTicketId() != null) {
            RepertoireTicket repertoireTicket = repertoireTicketService.getById(request.getRepertoireTicketId());
            if (repertoireTicket != null) {
                portfolioInfo.setCoverFront(repertoireTicket.getCoverFront());
                portfolioInfo.setCoverReverse(repertoireTicket.getCoverReverse());
                portfolioInfo.setCommonImage(repertoireTicket.getCommonImage());
            }
        }

        if (request.getDigitalAvatarId() != null) {
            DigitalAvatar digitalAvatar = digitalAvatarService.getById(request.getDigitalAvatarId());
            if (digitalAvatar != null) {
                portfolioInfo.setDigitalAvatarCommonImage(digitalAvatar.getCommonImage());
            }
        }

        // 保存新的PortfolioInfo
        portfolioInfoService.save(portfolioInfo);

        return request.getId();
    }

    /**
     * 删除Portfolio与PortfolioInfo关联记录
     *
     * @param ids Portfolio ID数组
     * @return 删除记录数
     */
    @Override
    @Transactional
    public Integer portfolioInfoDelete(Long[] ids) {
        // 判断是否有用户领取记录
        Long count = userReceivingRecordsService.findPortfolioCountById(ids);
        if (count > 0) {
            throw new ServiceException("已有用户领取，无法删除");
        }

        // 删除Portfolio记录（逻辑删除）
        removeBatchByIds(Arrays.asList(ids));

        // 关闭对应的PortfolioInfo记录
        for (Long id : ids) {
            portfolioInfoService.updateStatus(id);
        }

        return ids.length;
    }
}
