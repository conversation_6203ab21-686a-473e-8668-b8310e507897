package com.youying.system.domain.portfolio;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

/**
 * Portfolio与PortfolioInfo组合请求DTO
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class PortfolioWithInfoRequest {

    /**
     * Portfolio ID
     */
    private Long id;

    /**
     * 票类型
     */
    private Integer ticketType;

    /**
     * ocr编号
     */
    private Long ocrNo;

    /**
     * 腾讯识别ID
     */
    private Long scanningId;

    /**
     * 剧目剧场关联编号
     */
    private String no;

    /**
     * 组合名称
     */
    private String name;

    /**
     * 批次
     */
    private Integer batch;

    /**
     * 商家ID
     */
    private Long merchantId;

    /**
     * 剧场ID
     */
    private Long theaterId;

    /**
     * 剧目ID
     */
    private Long repertoireId;

    /**
     * 电子票ID
     */
    private Long repertoireTicketId;

    /**
     * 数字头像ID
     */
    private Long digitalAvatarId;

    /**
     * 组合介绍
     */
    private String introduction;

    /**
     * 发放数量
     */
    private Integer issuedQuantity;

    /**
     * 发放时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 免费发放
     */
    private Integer free;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 免责声明ID
     */
    private Long portfolioStatementId;

    /**
     * 免责声明
     */
    private String statement;

    /**
     * 座位是否可重复
     */
    private Integer seatStatus;

    /**
     * 橱窗展示状态
     */
    private Integer lookStatus;

    /**
     * 售罄状态
     */
    private Integer soldOut;

    /**
     * 审核状态
     */
    private Integer audit;

    /**
     * 审核标志
     */
    private Integer auditFlag;

    /**
     * 审核通过时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditPassTime;

    /**
     * 修改原因
     */
    private String updateCause;

    /**
     * 驳回原因
     */
    private String reasonsRejection;

    /**
     * 状态
     */
    private Integer status;

    // PortfolioInfo 特有字段
    /**
     * 封面版式正面
     */
    private String coverFront;

    /**
     * 封面版式反面
     */
    private String coverReverse;

    /**
     * 普通电子票图片
     */
    private String commonImage;

    /**
     * 普通数字头像图片
     */
    private String digitalAvatarCommonImage;
}
