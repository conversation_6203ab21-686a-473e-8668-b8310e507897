package com.youying.system.cache;

import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.youying.common.cache.CacheRefreshStrategy;
import com.youying.common.constant.CacheConstants;
import com.youying.common.core.redis.RedisCache;
import com.youying.common.event.DatabaseChangeEvent;

import lombok.extern.slf4j.Slf4j;

/**
 * 剧目表缓存刷新策略
 * 当t_repertoire表发生变更时，清理相关的Redis缓存
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RepertoireCacheRefreshStrategy implements CacheRefreshStrategy {

    @Autowired
    private RedisCache redisCache;

    @Override
    public String getSupportedTableName() {
        return "t_repertoire";
    }

    @Override
    public void refreshCache(DatabaseChangeEvent event) {
        log.info("Refreshing repertoire cache for operation: {}, primaryKey: {}",
                event.getOperationType(), event.getPrimaryKey());

        try {
            // 清理剧目相关的所有缓存
            clearRepertoireCache(event);

            log.info("Successfully refreshed repertoire cache for primaryKey: {}", event.getPrimaryKey());
        } catch (Exception e) {
            log.error("Error refreshing repertoire cache for primaryKey {}: {}",
                    event.getPrimaryKey(), e.getMessage(), e);
        }
    }

    /**
     * 清理剧目相关缓存
     */
    private void clearRepertoireCache(DatabaseChangeEvent event) {
        Object primaryKey = event.getPrimaryKey();

        // 1. 清理剧目详情缓存
        if (primaryKey != null) {
            String detailCacheKey = CacheConstants.REPERTOIRE_INFO_CACHE_KEY + primaryKey;
            redisCache.deleteObject(detailCacheKey);
            log.debug("Cleared repertoire detail cache: {}", detailCacheKey);
        }

        // 2. 清理剧目列表相关缓存（使用模糊匹配）
        clearCacheByPattern(CacheConstants.REPERTOIRE_LIST_CACHE_KEY + "*");

        // 3. 清理剧目通用缓存
        clearCacheByPattern(CacheConstants.REPERTOIRE_CACHE_KEY + "*");

        clearCacheByPattern("repertoire_cache_list");

        // 4. 根据操作类型执行特定的缓存清理
        switch (event.getOperationType()) {
            case INSERT:
                handleInsertCache(event);
                break;
            case UPDATE:
                handleUpdateCache(event);
                break;
            case DELETE:
                handleDeleteCache(event);
                break;
        }
    }

    /**
     * 处理INSERT操作的缓存清理
     */
    private void handleInsertCache(DatabaseChangeEvent event) {
        // 新增剧目时，需要清理列表缓存，因为列表内容发生了变化
        log.debug("Handling INSERT cache refresh for repertoire: {}", event.getPrimaryKey());

        // 清理推荐剧目缓存
        redisCache.deleteObject("repertoire:recommend:list");

        // 清理首页剧目缓存
        redisCache.deleteObject("repertoire:home:list");

        // 清理分类剧目缓存
        clearCacheByPattern("repertoire:category:*");
    }

    /**
     * 处理UPDATE操作的缓存清理
     */
    private void handleUpdateCache(DatabaseChangeEvent event) {
        // 更新剧目时，需要清理详情缓存和相关列表缓存
        log.debug("Handling UPDATE cache refresh for repertoire: {}", event.getPrimaryKey());

        Object primaryKey = event.getPrimaryKey();
        if (primaryKey != null) {
            // 清理剧目详情缓存
            String detailKey = CacheConstants.REPERTOIRE_INFO_CACHE_KEY + primaryKey;
            redisCache.deleteObject(detailKey);

            // 清理剧目相关的统计缓存
            redisCache.deleteObject("repertoire:stats:" + primaryKey);

            // 清理评分相关缓存
            redisCache.deleteObject("repertoire:rating:" + primaryKey);
        }

        // 清理可能受影响的列表缓存
        clearCacheByPattern("repertoire:search:*");
        clearCacheByPattern("repertoire:merchant:*");
    }

    /**
     * 处理DELETE操作的缓存清理
     */
    private void handleDeleteCache(DatabaseChangeEvent event) {
        // 删除剧目时，需要清理所有相关缓存
        log.debug("Handling DELETE cache refresh for repertoire: {}", event.getPrimaryKey());

        Object primaryKey = event.getPrimaryKey();
        if (primaryKey != null) {
            // 清理剧目详情缓存
            String detailKey = CacheConstants.REPERTOIRE_INFO_CACHE_KEY + primaryKey;
            redisCache.deleteObject(detailKey);

            // 清理剧目相关的所有缓存
            clearCacheByPattern("repertoire:*:" + primaryKey);
            clearCacheByPattern("repertoire:*:" + primaryKey + ":*");
        }

        // 清理列表缓存
        clearCacheByPattern("repertoire:list:*");
        clearCacheByPattern("repertoire:count:*");
    }

    /**
     * 根据模式清理缓存
     */
    private void clearCacheByPattern(String pattern) {
        try {
            Set<String> keys = redisCache.keys(pattern);
            if (keys != null && !keys.isEmpty()) {
                redisCache.deleteObject(keys);
                log.debug("Cleared {} cache keys matching pattern: {}", keys.size(), pattern);
            }
        } catch (Exception e) {
            log.warn("Failed to clear cache by pattern {}: {}", pattern, e.getMessage());
        }
    }

    @Override
    public int getPriority() {
        return 10; // 高优先级
    }

    @Override
    public boolean isAsyncSupported() {
        return true; // 支持异步执行
    }
}
