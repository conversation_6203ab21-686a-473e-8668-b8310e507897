package com.youying.system.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.RepertoireTicket;
import com.youying.system.domain.repertoireticket.RepertoireTicketRequest;
import com.youying.system.domain.repertoireticket.RepertoireTicketResponse;

/**
 * <p>
 * 剧目电子票表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface RepertoireTicketMapper extends BaseMapper<RepertoireTicket> {

    /**
     * 查询剧目电子票表列表(分页)
     *
     * @param request
     * @return
     */
    List<RepertoireTicketResponse> listByPage(RepertoireTicketRequest request);

    /**
     * 修改电子票封面
     *
     * @param id
     * @param commonImage
     */

    @Update("UPDATE t_repertoire_ticket SET `common_image` = #{commonImage} WHERE id = #{id}")
    void updateCommonImage(Long id, String commonImage);
}
