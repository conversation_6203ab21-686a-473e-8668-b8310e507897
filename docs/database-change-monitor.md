# 数据库变更监控和缓存刷新系统

## 概述

本系统基于Spring Boot事件机制实现了数据库变更监控和Redis缓存自动刷新功能。当监控的数据库表发生INSERT、UPDATE、DELETE操作时，系统会自动发布事件并执行相应的缓存刷新策略。

## 系统架构

### 核心组件

1. **DatabaseChangeEvent** - 数据库变更事件
2. **DatabaseChangeInterceptor** - MyBatis拦截器，监控SQL执行
3. **CacheRefreshStrategy** - 缓存刷新策略接口
4. **CacheRefreshManager** - 缓存刷新管理器
5. **DatabaseChangeMonitorConfig** - 自动配置类

### 工作流程

```
SQL执行 -> MyBatis拦截器 -> 发布变更事件 -> 缓存刷新管理器 -> 执行刷新策略 -> 清理Redis缓存
```

## 配置说明

### application.yml配置

```yaml
database:
  change:
    monitor:
      # 是否启用数据库变更监控
      enabled: true
      # 需要监控的表名列表
      monitored-tables:
        - t_repertoire
        - t_theater
      # 是否启用异步处理
      async-enabled: true
      # 异步处理线程池大小
      async-thread-pool-size: 5
      # 是否记录详细日志
      verbose-logging: false
      # 缓存刷新超时时间（毫秒）
      cache-refresh-timeout: 5000
```

### MyBatis配置

在`mybatis-config.xml`中已自动配置了拦截器：

```xml
<plugins>
    <plugin interceptor="com.youying.framework.interceptor.DatabaseChangeInterceptor">
        <!-- 可选：通过属性配置监控的表名 -->
        <!-- <property name="monitoredTables" value="t_repertoire,t_theater"/> -->
    </plugin>
</plugins>
```

## 使用方法

### 1. 为新表添加监控

#### 步骤1：更新配置

在`application.yml`中添加新的表名：

```yaml
database:
  change:
    monitor:
      monitored-tables:
        - t_repertoire
        - t_theater
        - t_your_new_table  # 添加新表
```

#### 步骤2：创建缓存刷新策略

创建实现`CacheRefreshStrategy`接口的类：

```java
@Slf4j
@Component
public class YourTableCacheRefreshStrategy implements CacheRefreshStrategy {

    @Autowired
    private RedisCache redisCache;

    @Override
    public String getSupportedTableName() {
        return "t_your_new_table";
    }

    @Override
    public void refreshCache(DatabaseChangeEvent event) {
        log.info("Refreshing cache for table: {}, operation: {}, primaryKey: {}",
                event.getTableName(), event.getOperationType(), event.getPrimaryKey());

        try {
            // 实现具体的缓存清理逻辑
            clearRelatedCache(event);
            
            log.info("Successfully refreshed cache for primaryKey: {}", event.getPrimaryKey());
        } catch (Exception e) {
            log.error("Error refreshing cache: {}", e.getMessage(), e);
        }
    }

    private void clearRelatedCache(DatabaseChangeEvent event) {
        Object primaryKey = event.getPrimaryKey();
        
        // 清理详情缓存
        if (primaryKey != null) {
            String detailKey = "your_table:info:" + primaryKey;
            redisCache.deleteObject(detailKey);
        }
        
        // 清理列表缓存
        clearCacheByPattern("your_table:list:*");
        
        // 根据操作类型执行特定清理
        switch (event.getOperationType()) {
            case INSERT:
                handleInsertCache(event);
                break;
            case UPDATE:
                handleUpdateCache(event);
                break;
            case DELETE:
                handleDeleteCache(event);
                break;
        }
    }

    private void clearCacheByPattern(String pattern) {
        try {
            Set<String> keys = redisCache.keys(pattern);
            if (keys != null && !keys.isEmpty()) {
                redisCache.deleteObject(keys);
                log.debug("Cleared {} cache keys matching pattern: {}", keys.size(), pattern);
            }
        } catch (Exception e) {
            log.warn("Failed to clear cache by pattern {}: {}", pattern, e.getMessage());
        }
    }

    // 实现其他处理方法...

    @Override
    public int getPriority() {
        return 100; // 优先级，数字越小优先级越高
    }

    @Override
    public boolean isAsyncSupported() {
        return true; // 是否支持异步执行
    }
}
```

#### 步骤3：添加缓存常量

在`CacheConstants`中添加新的缓存key常量：

```java
/**
 * 新表相关缓存 redis key
 */
public static final String YOUR_TABLE_CACHE_KEY = "your_table:";
public static final String YOUR_TABLE_LIST_CACHE_KEY = "your_table:list:";
public static final String YOUR_TABLE_INFO_CACHE_KEY = "your_table:info:";
```

### 2. 手动触发缓存刷新

```java
@Autowired
private CacheRefreshManager cacheRefreshManager;

// 手动触发缓存刷新
cacheRefreshManager.manualRefresh("t_repertoire", 
    DatabaseChangeEvent.OperationType.UPDATE, 123L);
```

### 3. 查看支持的表

```java
@Autowired
private CacheRefreshManager cacheRefreshManager;

// 获取所有支持的表名
List<String> supportedTables = cacheRefreshManager.getSupportedTables();
log.info("Supported tables: {}", supportedTables);
```

## 现有实现

### Repertoire表缓存刷新策略

- 清理剧目详情缓存：`repertoire:info:{id}`
- 清理剧目列表缓存：`repertoire:list:*`
- 清理推荐剧目缓存：`repertoire:recommend:list`
- 清理分类剧目缓存：`repertoire:category:*`
- 清理搜索结果缓存：`repertoire:search:*`

### Theater表缓存刷新策略

- 清理剧场详情缓存：`theater:info:{id}`
- 清理剧场列表缓存：`theater:list:*`
- 清理推荐剧场缓存：`theater:recommend:list`
- 清理地区剧场缓存：`theater:area:*`
- 清理商家剧场缓存：`theater:merchant:*`

## 注意事项

1. **性能考虑**：缓存刷新默认是异步执行的，不会影响主业务流程
2. **异常处理**：缓存刷新失败不会影响数据库操作的成功
3. **日志记录**：系统会记录详细的缓存刷新日志，便于调试和监控
4. **配置灵活**：可以通过配置文件灵活控制监控的表和刷新策略
5. **扩展性**：新增表的监控只需要实现缓存刷新策略接口即可

## 故障排除

### 常见问题

1. **事件未触发**：检查表名是否在监控列表中
2. **缓存未清理**：检查缓存刷新策略是否正确实现
3. **性能问题**：考虑调整异步线程池大小或优化缓存清理逻辑

### 调试方法

1. 开启详细日志：设置`verbose-logging: true`
2. 查看事件发布日志：关注`DatabaseChangeEvent`相关日志
3. 监控缓存刷新执行：关注`CacheRefreshStrategy`执行日志
